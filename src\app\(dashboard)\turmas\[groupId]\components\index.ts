// Contexto e estado
export { ClassGroupProvider, useClassGroup } from './ClassGroupContext';

// Componentes funcionais
export { ClassGroupTabNavigation } from './ClassGroupTabNavigation';
export { ClassGroupOverview } from './ClassGroupOverview';
export { ClassGroupCalendar } from './ClassGroupCalendar';
export { ClassGroupTimeline } from './ClassGroupTimeline';
export { ClassGroupStatsSection } from './ClassGroupStatsSection';
export { ClassGroupQuickActionBar } from './ClassGroupQuickActionBar';
export { ClassGroupIntegrationTest } from './ClassGroupIntegrationTest';
export { ClassGroupDetailsClient } from './ClassGroupDetailsClient';

// Formulário de edição
export { default as ClassGroupEditForm } from './form/ClassGroupEditForm';