'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { formatDateTimeBrazil } from '@/utils/format';
import { 
  User, 
  MapPin, 
  Users, 
  Calendar,
  Clock,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface ClassGroupOverviewProps {
  classGroup: ClassGroupWithDetails;
}

export function ClassGroupOverview({ classGroup }: ClassGroupOverviewProps) {
  // Função para gerar as iniciais do nome (seguindo padrão dos instrutores)
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const getCategoryDisplay = (category: string | null) => {
    const categories = {
      'kids': 'Infantil',
      'teens': 'Juvenil', 
      'adults': 'Adulto',
      'seniors': 'Sênior'
    };
    return category ? categories[category as keyof typeof categories] || category : 'Não especificada';
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Ativa' : 'Inativa'}
      </Badge>
    );
  };

  return (
    <div className="grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {/* Informações Básicas */}
      <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700">
        <CardHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
          <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <User className="h-4 w-4 sm:h-5 sm:w-5 text-slate-600 dark:text-gray-400" />
            Informações Básicas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 sm:space-y-4 px-4 sm:px-6 pb-4 sm:pb-6">
          <div className="flex items-center justify-between">
            <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Status:</span>
            {getStatusBadge(classGroup.is_active ?? false)}
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-500" />
              <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Instrutor:</span>
            </div>
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6 sm:h-8 sm:w-8 border-2 border-muted">
                <AvatarImage
                  src={classGroup.instructor.avatar_url || ""}
                  alt={classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`}
                />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-xs sm:text-sm">
                  {getInitials(classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`)}
                </AvatarFallback>
              </Avatar>
              <span className="text-xs sm:text-sm font-medium text-slate-900 dark:text-gray-100 break-words">
                {classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`}
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <MapPin className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-500" />
              <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Filial:</span>
            </div>
            <p className="text-xs sm:text-sm font-medium pl-5 sm:pl-6 text-slate-900 dark:text-gray-100 break-words">
              {classGroup.branch.name}
            </p>
          </div>

          <div className="pt-2">
            <Badge variant="outline" className="text-xs">
              {getCategoryDisplay(classGroup.category)}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Capacidade e Matrículas */}
      <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700">
        <CardHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
          <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <Users className="h-4 w-4 sm:h-5 sm:w-5 text-slate-600 dark:text-gray-400" />
            Capacidade
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 sm:space-y-4 px-4 sm:px-6 pb-4 sm:pb-6">
          <div className="flex items-center justify-between">
            <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Matriculados:</span>
            <span className="font-semibold text-slate-900 dark:text-gray-100 text-sm sm:text-base">
              {classGroup._count.enrollments}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Capacidade máxima:</span>
            <span className="font-semibold text-slate-900 dark:text-gray-100 text-sm sm:text-base">
              {classGroup.max_capacity || 'Ilimitada'}
            </span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Lista de espera:</span>
              <div className="flex items-center gap-2">
                {classGroup.allow_waitlist && (
                  <span className="font-semibold text-slate-900 dark:text-gray-100 text-sm sm:text-base">
                    {classGroup._count.waitlist || 0}
                  </span>
                )}
                {!classGroup.allow_waitlist && (
                  <Badge variant="secondary" className="text-xs flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    <span className="hidden sm:inline">Desativada</span>
                    <span className="sm:hidden">Off</span>
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {classGroup.capacity_usage_percentage && classGroup.max_capacity && (
            <div className="pt-2 sm:pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Taxa de ocupação</span>
                <span className="text-xs sm:text-sm font-medium text-slate-900 dark:text-gray-100">
                  {classGroup.capacity_usage_percentage.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-slate-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(classGroup.capacity_usage_percentage, 100)}%` }}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Estatísticas Rápidas */}
      <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700">
        <CardHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
          <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-slate-600 dark:text-gray-400" />
            Estatísticas
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
          <div className="grid grid-cols-1 gap-3 sm:gap-4">
            <div className="text-center p-3 sm:p-4 bg-slate-50 dark:bg-gray-800 rounded-lg">
              <div className="text-xl sm:text-2xl font-bold text-blue-600 dark:text-blue-400">
                {classGroup._count.classes}
              </div>
              <div className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">
                Total de aulas
              </div>
            </div>

            <div className="text-center p-3 sm:p-4 bg-slate-50 dark:bg-gray-800 rounded-lg">
              <div className="text-xl sm:text-2xl font-bold text-green-600 dark:text-green-400">
                {classGroup._count.enrollments}
              </div>
              <div className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">
                Alunos ativos
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Período de Funcionamento */}
      {(classGroup.start_date || classGroup.end_date) && (
        <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700 col-span-1 md:col-span-2 lg:col-span-3">
          <CardHeader className="pb-3 px-4 sm:px-6 pt-4 sm:pt-6">
            <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
              <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-slate-600 dark:text-gray-400" />
              Período de Funcionamento
            </CardTitle>
          </CardHeader>
          <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2">
              {classGroup.start_date && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-500" />
                    <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Data de início:</span>
                  </div>
                  <p className="text-xs sm:text-sm font-medium pl-5 sm:pl-6 text-slate-900 dark:text-gray-100 break-words">
                    {formatDateTimeBrazil(classGroup.start_date)}
                  </p>
                </div>
              )}

              {classGroup.end_date && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-500" />
                    <span className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">Data de término:</span>
                  </div>
                  <p className="text-xs sm:text-sm font-medium pl-5 sm:pl-6 text-slate-900 dark:text-gray-100 break-words">
                    {formatDateTimeBrazil(classGroup.end_date)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 