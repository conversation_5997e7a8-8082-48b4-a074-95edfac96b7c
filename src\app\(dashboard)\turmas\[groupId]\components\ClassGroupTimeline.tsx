'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  Users, 
  MapPin, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Play,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { getClassesByGroup } from '@/app/(dashboard)/aulas/actions';
import { toast } from 'sonner';
import { useClassGroup } from './ClassGroupContext';
import { parseLocalDate } from '@/utils/format';

interface ClassGroupTimelineProps {
  classGroup: ClassGroupWithDetails;
}

interface TimelineEvent {
  id: string;
  type: 'class' | 'milestone' | 'activity';
  title: string;
  description: string;
  date: Date;
  endDate?: Date;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  icon: React.ReactNode;
  color: string;
  details?: {
    instructor?: string;
    location?: string;
    duration?: string;
    attendees?: number;
  };
}

export function ClassGroupTimeline({ classGroup }: ClassGroupTimelineProps) {
  const { state, setTimelineEvents } = useClassGroup();
  const [events, setEvents] = useState<TimelineEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar eventos da timeline
  const loadTimelineEvents = async () => {
    if (!classGroup?.id) return;

    setIsLoading(true);
    setError(null);

    try {
             // Buscar aulas da turma
       const result = await getClassesByGroup(classGroup.id, {
         limit: 20
       });

      if (result.success && 'data' in result && result.data) {
        const classEvents: TimelineEvent[] = result.data.data.map((classItem: any) => {
          const startTime = new Date(classItem.start_time);
          const now = new Date();
          const endTime = new Date(classItem.end_time);
          
          let status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled' = 'upcoming';
          let icon = <Calendar className="h-4 w-4" />;
          let color = 'blue';

          if (classItem.status === 'cancelled') {
            status = 'cancelled';
            icon = <XCircle className="h-4 w-4" />;
            color = 'red';
          } else if (classItem.status === 'completed') {
            status = 'completed';
            icon = <CheckCircle className="h-4 w-4" />;
            color = 'green';
          } else if (now >= startTime && now <= endTime) {
            status = 'ongoing';
            icon = <Play className="h-4 w-4" />;
            color = 'orange';
          } else if (now > endTime) {
            status = 'completed';
            icon = <CheckCircle className="h-4 w-4" />;
            color = 'green';
          }

          return {
            id: classItem.id,
            type: 'class' as const,
            title: classItem.title || classItem.name || `Aula de ${classGroup.name}`,
            description: classItem.notes || 'Aula regular da turma',
            date: startTime,
            endDate: endTime,
            status,
            icon,
            color,
            details: {
              instructor: classItem.instructor?.full_name || classItem.instructor?.first_name,
              location: classItem.branch?.name,
              duration: `${Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60))} min`,
              attendees: classItem._count?.attendance || 0,
            },
          };
        });

        // Adicionar marcos importantes
        const milestones: TimelineEvent[] = [];
        
        // Marco de início da turma
        if (classGroup.start_date) {
          const startDate = parseLocalDate(classGroup.start_date);
          if (startDate) {
            milestones.push({
              id: 'start-milestone',
              type: 'milestone',
              title: 'Início da Turma',
              description: `Turma ${classGroup.name} iniciada`,
              date: startDate,
              status: new Date() > startDate ? 'completed' : 'upcoming',
              icon: <Users className="h-4 w-4" />,
              color: 'purple',
            });
          }
        }

        // Marco de fim da turma
        if (classGroup.end_date) {
          const endDate = parseLocalDate(classGroup.end_date);
          if (endDate) {
            milestones.push({
              id: 'end-milestone',
              type: 'milestone',
              title: 'Fim da Turma',
              description: `Conclusão prevista da turma ${classGroup.name}`,
              date: endDate,
              status: new Date() > endDate ? 'completed' : 'upcoming',
              icon: <AlertCircle className="h-4 w-4" />,
              color: 'gray',
            });
          }
        }

        // Combinar e ordenar eventos
        const allEvents = [...classEvents, ...milestones]
          .sort((a, b) => b.date.getTime() - a.date.getTime());

        setEvents(allEvents);
        
        // Atualizar contexto
        setTimelineEvents(allEvents);
      } else {
        throw new Error('Erro ao carregar eventos da timeline');
      }
    } catch (error) {
      console.error('Erro ao carregar timeline:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar timeline';
      setError(errorMessage);
      toast.error("Erro ao carregar timeline", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar dados na inicialização
  useEffect(() => {
    loadTimelineEvents();
  }, [classGroup?.id]);

  // Função para obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900';
      case 'ongoing': return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900';
      case 'cancelled': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900';
      default: return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900';
    }
  };

  // Função para obter texto do status
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Concluída';
      case 'ongoing': return 'Em andamento';
      case 'cancelled': return 'Cancelada';
      default: return 'Agendada';
    }
  };

  // Estado de loading
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-2" />
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-64" />
          </div>
          <div className="animate-pulse">
            <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
        </div>
        
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-start space-x-4">
                <div className="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full" />
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2" />
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Estado de erro
  if (error && events.length === 0) {
    return (
      <Card className="border-red-200 dark:border-red-800">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center flex-col space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
                Erro ao carregar timeline
              </h3>
              <p className="text-red-600 dark:text-red-400 mt-1">
                {error}
              </p>
            </div>
            <Button onClick={loadTimelineEvents} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Tentar novamente
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header com refresh */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-0 sm:items-center sm:justify-between">
        <div>
          <h2 className="text-lg sm:text-xl font-semibold text-slate-900 dark:text-gray-100">
            Timeline de Eventos
          </h2>
          <p className="text-slate-600 dark:text-gray-400 text-xs sm:text-sm">
            Próximas aulas e marcos importantes da turma
          </p>
        </div>
        <Button
          onClick={loadTimelineEvents}
          variant="outline"
          size="sm"
          disabled={isLoading}
          className="self-start sm:self-auto"
        >
          <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          <span className="text-xs sm:text-sm">Atualizar</span>
        </Button>
      </div>

      {/* Timeline */}
      {events.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-slate-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 dark:text-gray-100 mb-2">
                Nenhum evento encontrado
              </h3>
              <p className="text-slate-600 dark:text-gray-400">
                Não há aulas ou eventos agendados para esta turma no momento.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative">
          {/* Linha da timeline */}
          <div className="absolute left-4 sm:left-5 top-0 bottom-0 w-0.5 bg-slate-200 dark:bg-gray-700" />

          {/* Eventos */}
          <div className="space-y-4 sm:space-y-6">
            {events.map((event, index) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="relative flex items-start space-x-3 sm:space-x-4"
              >
                {/* Ícone do evento */}
                <div className={`relative z-10 flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full border-2 border-white dark:border-gray-900 ${getStatusColor(event.status)}`}>
                  <div className="scale-75 sm:scale-100">
                    {event.icon}
                  </div>
                </div>

                {/* Conteúdo do evento */}
                <div className="flex-1 min-w-0">
                  <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                    <CardContent className="p-3 sm:p-4">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 sm:gap-0">
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                            <h3 className="text-base sm:text-lg font-semibold text-slate-900 dark:text-gray-100 break-words">
                              {event.title}
                            </h3>
                            <Badge variant="outline" className={`${getStatusColor(event.status)} text-xs self-start sm:self-auto`}>
                              {getStatusText(event.status)}
                            </Badge>
                          </div>

                          <p className="text-slate-600 dark:text-gray-400 mb-3 text-sm sm:text-base">
                            {event.description}
                          </p>
                          
                          {/* Detalhes do evento */}
                          {event.details && (
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-xs sm:text-sm">
                              {event.details.instructor && (
                                <div className="flex items-center gap-1 sm:gap-2">
                                  <Users className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-400 flex-shrink-0" />
                                  <span className="text-slate-600 dark:text-gray-400 truncate">
                                    {event.details.instructor}
                                  </span>
                                </div>
                              )}

                              {event.details.location && (
                                <div className="flex items-center gap-1 sm:gap-2">
                                  <MapPin className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-400 flex-shrink-0" />
                                  <span className="text-slate-600 dark:text-gray-400 truncate">
                                    {event.details.location}
                                  </span>
                                </div>
                              )}

                              {event.details.duration && (
                                <div className="flex items-center gap-1 sm:gap-2">
                                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-400 flex-shrink-0" />
                                  <span className="text-slate-600 dark:text-gray-400 truncate">
                                    {event.details.duration}
                                  </span>
                                </div>
                              )}

                              {event.details.attendees !== undefined && (
                                <div className="flex items-center gap-1 sm:gap-2">
                                  <Users className="h-3 w-3 sm:h-4 sm:w-4 text-slate-500 dark:text-gray-400 flex-shrink-0" />
                                  <span className="text-slate-600 dark:text-gray-400 truncate">
                                    {event.details.attendees} presentes
                                  </span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Data e hora */}
                        <div className="text-left sm:text-right sm:ml-4 flex-shrink-0">
                          <div className="text-xs sm:text-sm font-medium text-slate-900 dark:text-gray-100">
                            {event.date.toLocaleDateString('pt-BR', {
                              day: '2-digit',
                              month: 'short',
                            })}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-gray-400">
                            {event.date.toLocaleTimeString('pt-BR', {
                              hour: '2-digit',
                              minute: '2-digit',
                            })}
                            {event.endDate && (
                              <>
                                {' - '}
                                {event.endDate.toLocaleTimeString('pt-BR', {
                                  hour: '2-digit',
                                  minute: '2-digit',
                                })}
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 