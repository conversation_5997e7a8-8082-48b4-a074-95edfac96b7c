'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle2, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { 
  ClassGroupTabNavigation,
  ClassGroupOverview,
  ClassGroupTimeline,
  ClassGroupStatsSection,
  ClassGroupCalendar,
  ClassGroupQuickActionBar,
  useClassGroup
} from './';

export function ClassGroupDetailsClient() {
  const { state } = useClassGroup();
  const router = useRouter();
  
  if (!state.classGroup) {
    return null;
  }

  const classGroup = state.classGroup;

  const handleBackToList = () => {
    router.push('/turmas');
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Ativa' : 'Inativa'}
      </Badge>
    );
  };

  return (
    <div className="space-y-4 sm:space-y-6">

      {/* Header modernizado */}
      <div className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-lg p-4 sm:p-6">
        <div className="flex flex-col space-y-3 sm:space-y-4">
          {/* Botão de voltar */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToList}
              className="text-slate-600 hover:text-slate-900 dark:text-gray-400 dark:hover:text-gray-100 -ml-2 h-8 px-2 sm:h-9 sm:px-3"
            >
              <ArrowLeft className="h-4 w-4 mr-1 sm:mr-2" />
              <span className="text-sm sm:text-base">Voltar para turmas</span>
            </Button>
          </div>

          {/* Conteúdo principal do header */}
          <div className="flex flex-col space-y-3 sm:space-y-4 sm:flex-row sm:items-start sm:justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-slate-900 dark:text-gray-100 break-words">
                {classGroup.name}
              </h1>
              <p className="text-sm sm:text-base text-slate-600 dark:text-gray-400 mt-1 break-words">
                {classGroup.description || 'Sem descrição'}
              </p>
            </div>
            <div className="flex items-center justify-start sm:justify-end">
              {getStatusBadge(classGroup.is_active ?? false)}
            </div>
          </div>
        </div>
      </div>

      {/* QuickActionBar nova */}
      <ClassGroupQuickActionBar classGroup={classGroup} />

      {/* Sistema de abas novo */}
      <ClassGroupTabNavigation
        overviewContent={<ClassGroupOverview classGroup={classGroup} />}
        timelineContent={<ClassGroupTimeline classGroup={classGroup} />}
        statisticsContent={<ClassGroupStatsSection classGroup={classGroup} />}
        calendarContent={<ClassGroupCalendar classGroup={classGroup} />}
        defaultTab="overview"
      />
    </div>
  );
} 