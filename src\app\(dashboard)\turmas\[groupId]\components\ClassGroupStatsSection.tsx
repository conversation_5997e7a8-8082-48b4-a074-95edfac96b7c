'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Calendar, 
  Clock, 
  TrendingUp,
  User<PERSON>heck,
  UserX,
  AlertCircle,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { getClassGroupStats } from '../../actions/class-group';
import { getClassesByGroup } from '@/app/(dashboard)/aulas/actions';
import { toast } from 'sonner';
import { useClassGroup } from './ClassGroupContext';

interface ClassGroupStatsSectionProps {
  classGroup: ClassGroupWithDetails;
}

interface ClassGroupStats {
  enrollments: {
    active: number;
    waitlist: number;
    total: number;
    capacity: number;
  };
  classes: {
    total: number;
    completed: number;
    upcoming: number;
    cancelled: number;
  };
  attendance: {
    total_attendances: number;
    unique_students: number;
    average_per_class: number;
    attendance_rate: number;
  };
}

export function ClassGroupStatsSection({ classGroup }: ClassGroupStatsSectionProps) {
  const { state, setStatistics, setLoading } = useClassGroup();
  const [stats, setStats] = useState<ClassGroupStats | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carregar estatísticas
  const loadStats = async () => {
    if (!classGroup?.id) return;

    setIsRefreshing(true);
    setError(null);

    try {
      // Chamar a API com o formato correto
      const result = await getClassGroupStats({
        class_group_id: classGroup.id,
        period_start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        period_end: new Date().toISOString(),
      });
      
      if (result.success && 'data' in result && result.data) {
        // Buscar aulas detalhadas da turma para calcular estatísticas por status
        const classesResult = await getClassesByGroup(classGroup.id);
        let classesData: any[] = [];
        
        if (classesResult.success && 'data' in classesResult && classesResult.data) {
          if (Array.isArray(classesResult.data)) {
            classesData = classesResult.data;
          } else if (classesResult.data.data && Array.isArray(classesResult.data.data)) {
            classesData = classesResult.data.data;
          }
        }
        
        // Calcular estatísticas por status
        const now = new Date();
        const completedClasses = classesData.filter(c => c.status === 'completed').length;
        const cancelledClasses = classesData.filter(c => c.status === 'cancelled').length;
        const upcomingClasses = classesData.filter(c => {
          const startTime = new Date(c.start_time);
          return c.status === 'scheduled' && startTime > now;
        }).length;
        
        const statsData: ClassGroupStats = {
          enrollments: {
            active: result.data.enrollments?.active || 0,
            waitlist: result.data.waitlist?.count || 0,
            total: (result.data.enrollments?.active || 0) + (result.data.waitlist?.count || 0),
            capacity: classGroup.max_capacity || 0,
          },
          classes: {
            total: classesData.length,
            completed: completedClasses,
            upcoming: upcomingClasses,
            cancelled: cancelledClasses,
          },
          attendance: {
            total_attendances: result.data.attendance?.total || 0,
            unique_students: result.data.attendance?.unique_students || 0,
            average_per_class: (result.data.attendance?.classes_in_period || 0) > 0 ? (result.data.attendance?.total || 0) / (result.data.attendance?.classes_in_period || 1) : 0,
            attendance_rate: result.data.attendance?.attendance_rate || 0,
          },
        };
        
        setStats(statsData);
        
        // Atualizar contexto
        setStatistics({
          enrollments: statsData.enrollments.active,
          waitlist: statsData.enrollments.waitlist,
          classes: statsData.classes.total,
          attendance: {
            present: statsData.attendance.total_attendances,
            absent: 0, // Calculado baseado nos dados
            total: statsData.attendance.total_attendances,
            rate: statsData.attendance.attendance_rate,
          },
        });
      } else {
        throw new Error('Erro ao carregar estatísticas');
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar estatísticas';
      setError(errorMessage);
      toast.error("Erro ao carregar estatísticas", {
        description: errorMessage,
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Carregar dados na inicialização
  useEffect(() => {
    loadStats();
  }, [classGroup?.id]);

  // Estado de loading
  if (!stats && !error) {
    return (
      <div className="space-y-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-3">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2" />
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Estado de erro
  if (error && !stats) {
    return (
      <Card className="border-red-200 dark:border-red-800">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center flex-col space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
                Erro ao carregar estatísticas
              </h3>
              <p className="text-red-600 dark:text-red-400 mt-1">
                {error}
              </p>
            </div>
            <Button onClick={loadStats} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Tentar novamente
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) return null;

  // Calcular métricas
  const capacityUsage = stats.enrollments.capacity > 0 
    ? (stats.enrollments.active / stats.enrollments.capacity) * 100 
    : 0;
  
  const completionRate = stats.classes.total > 0 
    ? (stats.classes.completed / stats.classes.total) * 100 
    : 0;

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header com refresh */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-0 sm:items-center sm:justify-between">
        <div>
          <h2 className="text-lg sm:text-xl font-semibold text-slate-900 dark:text-gray-100">
            Estatísticas Detalhadas
          </h2>
          <p className="text-slate-600 dark:text-gray-400 text-xs sm:text-sm">
            Métricas e análises da turma
          </p>
        </div>
        <Button
          onClick={loadStats}
          variant="outline"
          size="sm"
          disabled={isRefreshing}
          className="self-start sm:self-auto"
        >
          <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span className="text-xs sm:text-sm">Atualizar</span>
        </Button>
      </div>

      {/* Cards de estatísticas principais */}
      <div className="grid gap-3 sm:gap-4 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {/* Matrículas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-950/20">
            <CardHeader className="pb-2 sm:pb-3 px-3 sm:px-6 pt-3 sm:pt-6">
              <CardTitle className="text-xs sm:text-sm font-medium text-blue-700 dark:text-blue-300 flex items-center gap-1 sm:gap-2">
                <Users className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                Matrículas
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
              <div className="space-y-2">
                <div className="text-xl sm:text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {stats.enrollments.active}
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-blue-600 dark:text-blue-400 truncate">
                    Lista: {stats.enrollments.waitlist}
                  </span>
                  <span className="text-blue-600 dark:text-blue-400 truncate">
                    Cap: {stats.enrollments.capacity}
                  </span>
                </div>
                <Progress
                  value={capacityUsage}
                  className="h-2"
                />
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  {capacityUsage.toFixed(1)}% da capacidade
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Aulas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-950/20">
            <CardHeader className="pb-2 sm:pb-3 px-3 sm:px-6 pt-3 sm:pt-6">
              <CardTitle className="text-xs sm:text-sm font-medium text-green-700 dark:text-green-300 flex items-center gap-1 sm:gap-2">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                Aulas
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
              <div className="space-y-2">
                <div className="text-xl sm:text-2xl font-bold text-green-900 dark:text-green-100">
                  {stats.classes.total}
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-green-600 dark:text-green-400 truncate">
                    Concluídas: {stats.classes.completed}
                  </span>
                  <span className="text-green-600 dark:text-green-400 truncate">
                    Próximas: {stats.classes.upcoming}
                  </span>
                </div>
                <Progress
                  value={completionRate}
                  className="h-2"
                />
                <p className="text-xs text-green-600 dark:text-green-400">
                  {completionRate.toFixed(1)}% concluídas
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Frequência */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className="border-purple-200 dark:border-purple-800 bg-purple-50/50 dark:bg-purple-950/20">
            <CardHeader className="pb-2 sm:pb-3 px-3 sm:px-6 pt-3 sm:pt-6">
              <CardTitle className="text-xs sm:text-sm font-medium text-purple-700 dark:text-purple-300 flex items-center gap-1 sm:gap-2">
                <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                Frequência
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
              <div className="space-y-2">
                <div className="text-xl sm:text-2xl font-bold text-purple-900 dark:text-purple-100">
                  {stats.attendance.attendance_rate.toFixed(1)}%
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-purple-600 dark:text-purple-400 truncate">
                    Presenças: {stats.attendance.total_attendances}
                  </span>
                  <span className="text-purple-600 dark:text-purple-400 truncate">
                    Únicos: {stats.attendance.unique_students}
                  </span>
                </div>
                <Progress
                  value={stats.attendance.attendance_rate}
                  className="h-2"
                />
                <p className="text-xs text-purple-600 dark:text-purple-400">
                  {stats.attendance.average_per_class.toFixed(1)} por aula
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Capacidade */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card className="border-orange-200 dark:border-orange-800 bg-orange-50/50 dark:bg-orange-950/20">
            <CardHeader className="pb-2 sm:pb-3 px-3 sm:px-6 pt-3 sm:pt-6">
              <CardTitle className="text-xs sm:text-sm font-medium text-orange-700 dark:text-orange-300 flex items-center gap-1 sm:gap-2">
                <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                Capacidade
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
              <div className="space-y-2">
                <div className="text-xl sm:text-2xl font-bold text-orange-900 dark:text-orange-100">
                  {stats.enrollments.capacity}
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-orange-600 dark:text-orange-400 truncate">
                    Ocupado: {stats.enrollments.active}
                  </span>
                  <span className="text-orange-600 dark:text-orange-400 truncate">
                    Livre: {Math.max(0, stats.enrollments.capacity - stats.enrollments.active)}
                  </span>
                </div>
                <Progress
                  value={capacityUsage}
                  className="h-2"
                />
                <div className="flex items-center gap-1">
                  {capacityUsage >= 90 ? (
                    <Badge variant="destructive" className="text-xs">
                      Lotada
                    </Badge>
                  ) : capacityUsage >= 70 ? (
                    <Badge variant="secondary" className="text-xs">
                      Quase cheia
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">
                      Disponível
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Análise detalhada de frequência */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <Card>
          <CardHeader className="px-3 sm:px-6 pt-3 sm:pt-6 pb-2 sm:pb-3">
            <CardTitle className="flex items-center gap-1 sm:gap-2 text-sm sm:text-base">
              <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
              Análise de Frequência
            </CardTitle>
          </CardHeader>
          <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm font-medium">Total de Presenças</span>
                  <span className="text-base sm:text-lg font-bold">{stats.attendance.total_attendances}</span>
                </div>
                <Progress value={100} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm font-medium">Alunos Únicos</span>
                  <span className="text-base sm:text-lg font-bold">{stats.attendance.unique_students}</span>
                </div>
                <Progress
                  value={stats.enrollments.active > 0 ? (stats.attendance.unique_students / stats.enrollments.active) * 100 : 0}
                  className="h-2"
                />
              </div>

              <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm font-medium">Média por Aula</span>
                  <span className="text-base sm:text-lg font-bold">{stats.attendance.average_per_class.toFixed(1)}</span>
                </div>
                <Progress
                  value={stats.enrollments.active > 0 ? (stats.attendance.average_per_class / stats.enrollments.active) * 100 : 0}
                  className="h-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Gestão de capacidade */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.6 }}
      >
        <Card>
          <CardHeader className="px-3 sm:px-6 pt-3 sm:pt-6 pb-2 sm:pb-3">
            <CardTitle className="flex items-center gap-1 sm:gap-2 text-sm sm:text-base">
              <Users className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
              Gestão de Capacidade
            </CardTitle>
          </CardHeader>
          <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
            <div className="space-y-3 sm:space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 bg-slate-50 dark:bg-gray-800 rounded-lg gap-3 sm:gap-0">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900 rounded-lg flex-shrink-0">
                    <Users className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="min-w-0">
                    <p className="font-medium text-sm sm:text-base">Alunos Matriculados</p>
                    <p className="text-xs sm:text-sm text-slate-600 dark:text-gray-400 truncate">
                      {stats.enrollments.active} de {stats.enrollments.capacity} vagas
                    </p>
                  </div>
                </div>
                <div className="text-left sm:text-right">
                  <p className="text-xl sm:text-2xl font-bold">{stats.enrollments.active}</p>
                  <p className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">
                    {capacityUsage.toFixed(1)}%
                  </p>
                </div>
              </div>

              {stats.enrollments.waitlist > 0 && (
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 gap-3 sm:gap-0">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className="p-1.5 sm:p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex-shrink-0">
                      <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="min-w-0">
                      <p className="font-medium text-sm sm:text-base">Lista de Espera</p>
                      <p className="text-xs sm:text-sm text-yellow-700 dark:text-yellow-300">
                        Alunos aguardando vaga
                      </p>
                    </div>
                  </div>
                  <div className="text-left sm:text-right">
                    <p className="text-xl sm:text-2xl font-bold text-yellow-700 dark:text-yellow-300">
                      {stats.enrollments.waitlist}
                    </p>
                    <Badge variant="secondary" className="text-xs">
                      Em espera
                    </Badge>
                  </div>
                </div>
              )}

              {capacityUsage >= 90 && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                    <p className="font-medium text-red-900 dark:text-red-100">
                      Turma quase lotada
                    </p>
                  </div>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    A turma está com {capacityUsage.toFixed(1)}% da capacidade ocupada. 
                    Considere aumentar a capacidade ou criar uma nova turma.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
} 